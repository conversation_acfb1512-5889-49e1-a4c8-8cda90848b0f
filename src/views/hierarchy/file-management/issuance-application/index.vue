<template>
    <div class="min-h-300px mini-hellp-list">
        <n-search-table-page
            ref="searchTablePageRef"
            :data-table-props="{
                columns,
                size: 'small',
                scrollX: 1300,
                data: dataSource,
                maxHeight: 'calc(100vh - 64px)',
                rowKey: (row) => row.id,
                'onUpdate:checkedRowKeys': handleCheck
            }"
            :params="params"
            :pagination-props="{
                showQuickJumper: true,
                showSizePicker: true,
                pageSizes: [10, 15, 20, 30, 50, 100],
                'onUpdate:pageSize': handlePageSizeChange
            }"
            :search-table-space="{
                size: 20
            }"
            :search-props="{
                showAdd: false,
                searchInputPlaceholder: '请输入发放类型',
                inputWidth: '180px'
            }"
            @reset="onReset"
        >
            <template #search_form_middle>
                <n-input v-model:value="params.fileType" placeholder="输入文件类型" style="width: 180px" clearable />
            </template>
            <template #search_form_after>
                <n-button type="primary" @click="showMore = !showMore">{{ showMore ? '收起' : '更多' }}</n-button>
            </template>
            <template #search_bottom_layout>
                <n-collapse-transition v-if="showMore" :show="showMore">
                    <n-space>
                        <n-input v-model:value="applicant" placeholder="输入申请人" style="width: 180px" clearable />
                        <n-select
                            v-model:value="params.status"
                            :options="statusOptions"
                            placeholder="选择状态"
                            style="width: 180px"
                            clearable
                        />
                        <n-select
                            v-model:value="params.issuanceType"
                            :options="issuanceTypeOptions"
                            placeholder="选择发放类型"
                            style="width: 180px"
                            clearable
                        />

                        <select-tree-dictionary
                            v-model:value="params.category"
                            placeholder="选择文件类别"
                            :params="params.issuanceType"
                            multiple
                            style="width: 200px"
                            checkable
                            filterable
                            clearable
                            cascade
                            :show-path="false"
                            @change="onCategoryChange"
                        />
                        <select-tree-organization
                            v-model:value="params.department"
                            placeholder="选择编制部门"
                            multiple
                            checkable
                            cascade
                            style="width: 200px"
                            filterable
                            clearable
                            :show-path="false"
                            @change="onDepartmentChange"
                        />
                    </n-space>
                </n-collapse-transition>
            </template>
            <template #search_handle_after>
                <n-space justify="end">
                    <n-permission has="issuanceApplicationAdd">
                        <n-button type="primary" @click="onAddEdit('add')">新增</n-button>
                    </n-permission>
                    <n-permission has="issuanceApplicationExport">
                        <n-button type="warning" @click="exportFile" :disabled="!checkedRowKeys.length">导出</n-button>
                    </n-permission>
                </n-space>
            </template>
            <template #table_reviewer="{ row }">
                <div>
                    <template
                        v-for="(r, idx) in (Array.isArray(row.reviewer) ? row.reviewer : row.reviewer ? [{ name: row.reviewer, date: row.reviewerDate }] : []) as Array<{ name: string; date?: string }>"
                        :key="idx"
                    >
                        <div style="line-height: 1.5">{{ r.name }}{{ r.date ? ' ' + r.date : '' }}</div>
                    </template>
                </div>
            </template>
            <template #table_approver="{ row }">
                <div>
                    <template
                        v-for="(a, idx) in (Array.isArray(row.approver) ? row.approver : row.approver ? [{ name: row.approver, date: row.approverDate }] : []) as Array<{ name: string; date?: string }>"
                        :key="idx"
                    >
                        <div style="line-height: 1.5">{{ a.name }}{{ a.date ? ' ' + a.date : '' }}</div>
                    </template>
                </div>
            </template>
            <template #table_issueCount="{ row }">
                <n-popover trigger="hover" placement="top">
                    <template #trigger>
                        <span>{{ row.issueCount }}</span>
                    </template>
                    <div>
                        <template
                            v-for="(p, idx) in (() => { const persons: Array<{ name: string; status?: string; fileForm?: string }> = []; (row.itemList || []).forEach((item: any) => { (item.permissions || []).forEach((perm: any) => { (perm.personList || []).forEach((person: any) => { persons.push({ name: person.name, status: person.status, fileForm: perm.fileForm }); }); }); }); return persons; })()"
                            :key="idx"
                        >
                            <div>{{ p.name }} {{ p.status || '' }} {{ p.fileForm || '' }}</div>
                        </template>
                    </div>
                </n-popover>
            </template>
            <template #table_signCount="{ row }">
                <n-popover trigger="hover" placement="top">
                    <template #trigger>
                        <span>{{ row.signCount }}</span>
                    </template>
                    <div>
                        <template
                            v-for="(p, idx) in (() => { const persons: Array<{ name: string; fileForm?: string }> = []; (row.itemList || []).forEach((item: any) => { (item.permissions || []).forEach((perm: any) => { (perm.personList || []).forEach((person: any) => { persons.push({ name: person.name, fileForm: perm.fileForm }); }); }); }); return persons; })()"
                            :key="idx"
                        >
                            <div>{{ p.name }} {{ p.fileForm || '' }}</div>
                        </template>
                    </div>
                </n-popover>
            </template>
            <template #table_disposalCount="{ row }">
                <n-popover trigger="hover" placement="top">
                    <template #trigger>
                        <span>{{ row.disposalCount }}</span>
                    </template>
                    <div>
                        <template
                            v-for="(p, idx) in (() => { const persons: Array<{ name: string }> = []; (row.paperDisposalList || []).forEach((item: any) => { (item.personList || []).forEach((person: any) => { persons.push({ name: person.name }); }); }); return persons; })()"
                            :key="idx"
                        >
                            <div>{{ p.name }}</div>
                        </template>
                    </div>
                </n-popover>
            </template>
            <template #table_operation="{ row }">
                <n-space justify="center">
                    <n-permission has="issuanceApplicationCancel">
                        <n-button type="primary" size="tiny" @click="onCancel(row)">撤销</n-button>
                    </n-permission>
                    <n-permission has="issuanceApplicationRecycle">
                        <n-button type="warning" size="tiny" @click="onRecycle(row)">回收</n-button>
                    </n-permission>
                    <n-dropdown
                        v-if="todoOptions.length > 0"
                        trigger="click"
                        :options="todoOptions(row)"
                        @select="(key) => handleTodoMenu(key, row)"
                    >
                        <n-button size="tiny">更多</n-button>
                    </n-dropdown>
                </n-space>
            </template>
            <template #table_status="{ row }">
                <n-tag size="small" round :bordered="false" :type="statusTagType(row.status)">
                    {{ statusLabel(row.status) }}
                </n-tag>
            </template>
        </n-search-table-page>
    </div>
</template>

<script setup lang="ts">
import { IssuanceApplicationRow } from '@/api/apis/nebula/api/v1/issuance-application';
import useStore from '@/store/modules/main';
import { DataTableColumns, NTag, NPopover } from 'naive-ui';

const applicant = ref<string | null>(null);
const showMore = ref(false);

const issuanceTypeOptions = [
    { label: '内部发放', value: 'internal' },
    { label: '外部发放', value: 'external' }
];

const statusOptions = [
    { label: '待提交', value: 'pending' },
    { label: '待审批', value: 'pending_approval' },
    { label: '已驳回', value: 'rejected' },
    { label: '已审批', value: 'approved' }
];

const searchTablePageRef = ref();
const params = ref({
    fileType: '',
    applicant: '',
    status: null,
    issuanceType: null,
    category: null,
    department: null
});
const checkedRowKeys = ref<string[]>([]);
const dataSource = ref<IssuanceApplicationRow[]>([
    {
        id: '1',
        issuanceType: '内部发放',
        fileType: '技术文档',
        category: '开发规范',
        applyDate: '2024-01-15',
        expectedIssueDate: '2024-01-20',
        reviewer: '张三',
        approver: '李四',
        issueCount: 10,
        signCount: 8,
        disposalCount: 2,
        status: '已审批',
        applicant: '张三',
        reason: '新员工入职',
        otherReason: '',
        itemList: [
            {
                key: 1,
                fileName: '技术文档1',
                fileNo: 'FN001',
                version: 'v1.0',
                permissions: [
                    {
                        type: '内发-电子-查询',
                        fileForm: '电子文件',
                        filePermission: '查询',
                        receiver: null,
                        personList: [
                            { id: '1', name: '张三', status: '可选' },
                            { id: '2', name: '李四', status: '已回收', recycleDate: '2024-01-18' },
                            { id: '3', name: '王五', status: '流程中' },
                            { id: '4', name: '赵六', status: '已回收', recycleDate: '2024-01-19' },
                            { id: '5', name: '孙七', status: '可选' },
                            { id: '6', name: '周八', status: '可选' },
                            { id: '7', name: '吴九', status: '已回收', recycleDate: '2024-01-20' },
                            { id: '8', name: '郑十', status: '流程中' }
                        ]
                    },
                    {
                        type: '内发-电子-查询/下载',
                        fileForm: '电子文件',
                        filePermission: '查询/下载',
                        receiver: null,
                        personList: [
                            { id: '1', name: '张三', status: '可选' },
                            { id: '2', name: '李四', status: '流程中' },
                            { id: '3', name: '王五', status: '已回收', recycleDate: '2024-01-17' }
                        ]
                    },
                    {
                        type: '内发-纸质-一次下载',
                        fileForm: '纸质文件',
                        filePermission: '一次下载',
                        receiver: null,
                        personList: [
                            { id: '1', name: '张三', status: '可选' },
                            { id: '2', name: '李四', status: '可选' }
                        ]
                    }
                ]
            }
        ],
        paperDisposalList: [
            {
                key: 1,
                fileName: '技术文档1',
                fileNo: 'FN001',
                version: 'v1.0',
                fileForm: '纸质文件',
                issueCount: 10,
                signCount: 8,
                disposalCount: 2,
                personList: [
                    { id: '1', name: '张三', status: '可选' },
                    { id: '2', name: '李四', status: '可选' }
                ]
            }
        ]
    },
    {
        id: '2',
        issuanceType: '外部发放',
        fileType: '操作手册',
        category: '用户指南',
        applyDate: '2024-01-10',
        expectedIssueDate: '2024-01-18',
        reviewer: '王五',
        approver: '赵六',
        issueCount: 5,
        signCount: 3,
        disposalCount: 2,
        status: '待审批',
        applicant: '王五',
        reason: '岗位/职责调整',
        otherReason: '',
        itemList: [
            {
                key: 2,
                fileName: '操作手册1',
                fileNo: 'FN002',
                version: 'v1.1',
                permissions: [
                    {
                        type: '外发-电子-一次下载',
                        fileForm: '电子文件',
                        filePermission: '一次下载',
                        receiver: '外部客户A公司',
                        personList: [
                            { id: '3', name: '王五', status: '可选' },
                            { id: '4', name: '赵六', status: '流程中' },
                            { id: '5', name: '孙七', status: '已回收', recycleDate: '2024-01-16' },
                            { id: '6', name: '周八', status: '可选' },
                            { id: '9', name: '钱十一', status: '可选' },
                            { id: '10', name: '孙十二', status: '流程中' },
                            { id: '11', name: '李十三', status: '已回收', recycleDate: '2024-01-15' },
                            { id: '12', name: '周十四', status: '可选' },
                            { id: '13', name: '吴十五', status: '可选' },
                            { id: '14', name: '郑十六', status: '流程中' }
                        ]
                    },
                    {
                        type: '外发-纸质-一次下载',
                        fileForm: '纸质文件',
                        filePermission: '一次下载',
                        receiver: '外部客户A公司',
                        personList: [
                            { id: '3', name: '王五', status: '已回收', recycleDate: '2024-01-16' },
                            { id: '4', name: '赵六', status: '可选' },
                            { id: '5', name: '孙七', status: '流程中' },
                            { id: '6', name: '周八', status: '已回收', recycleDate: '2024-01-15' }
                        ]
                    }
                ]
            }
        ]
    },
    {
        id: '3',
        issuanceType: '内部发放',
        fileType: '管理制度',
        category: '规章制度',
        applyDate: '2024-01-12',
        expectedIssueDate: '2024-01-25',
        reviewer: '孙七',
        approver: '周八',
        issueCount: 8,
        signCount: 6,
        disposalCount: 1,
        status: '已驳回',
        applicant: '孙七',
        reason: '其他',
        otherReason: '特殊情况需要',
        itemList: [
            {
                key: 3,
                fileName: '管理制度1',
                fileNo: 'FN003',
                version: 'v2.0',
                permissions: [
                    {
                        type: '内发-纸质-一次下载',
                        fileForm: '纸质文件',
                        filePermission: '一次下载',
                        receiver: null,
                        personList: [
                            { id: '7', name: '孙七', status: '可选' },
                            { id: '15', name: '王十七', status: '已回收', recycleDate: '2024-01-22' },
                            { id: '16', name: '李十八', status: '流程中' },
                            { id: '17', name: '张十九', status: '可选' },
                            { id: '18', name: '刘二十', status: '已回收', recycleDate: '2024-01-23' }
                        ]
                    }
                ]
            }
        ]
    }
]);

const page = computed(() => {
    return searchTablePageRef.value?.getCurrentPage();
});
const pageSize = ref(10);
const handlePageSizeChange = (size: number) => {
    pageSize.value = size;
};
const getPageSizes = computed(() => {
    return pageSize.value;
});

const columns: DataTableColumns = [
    {
        type: 'selection' as const
    },
    {
        title: '序号',
        key: 'serialNumber',
        width: 60,
        render: (_: any, index: number) => {
            return `${(page.value - 1) * getPageSizes.value + (index + 1)}`;
        }
    },
    { title: '发放类型', key: 'issuanceType', align: 'center', ellipsis: { tooltip: true }, resizable: true },
    { title: '文件类型', key: 'fileType', align: 'center', ellipsis: { tooltip: true }, resizable: true },
    { title: '文件类别', key: 'category', align: 'center', ellipsis: { tooltip: true }, resizable: true },
    { title: '申请日期', key: 'applyDate', align: 'center', ellipsis: { tooltip: true }, resizable: true },
    { title: '希望发放日期', key: 'expectedIssueDate', align: 'center', ellipsis: { tooltip: true }, resizable: true },
    { title: '审核人', key: 'reviewer', align: 'center', ellipsis: { tooltip: true }, resizable: true },
    { title: '批准人', key: 'approver', align: 'center', ellipsis: { tooltip: true }, resizable: true },
    { title: '发放份数', key: 'issueCount', align: 'center', ellipsis: { tooltip: true }, resizable: true },
    { title: '签收份数', key: 'signCount', align: 'center', ellipsis: { tooltip: true }, resizable: true },
    { title: '处置份数', key: 'disposalCount', align: 'center', ellipsis: { tooltip: true }, resizable: true },
    { title: '状态', key: 'status', align: 'center', ellipsis: { tooltip: true } },
    { title: '操作', key: 'operation', align: 'center', width: 180, fixed: 'right' }
];

const store = useStore();

const todoOptions = (row: any) => {
    const options: any[] = [];
    const perms = store.permissions;
    if (perms.includes('issuanceApplicationEdit') && ['待审批', '已驳回'].includes(row.status)) {
        options.push({
            label: '编辑',
            key: 'edit'
        });
    }
    if (perms.includes('issuanceApplicationDistributeDetail')) {
        options.push({
            label: '发放回收详情',
            key: 'detail'
        });
    }
    if (perms.includes('issuanceApplicationPaperDispose')) {
        options.push({
            label: '纸质文件处置',
            key: 'dispose'
        });
    }
    if (perms.includes('issuanceApplicationPaperDetail')) {
        options.push({
            label: '纸质文件处置详情',
            key: 'paperDetail'
        });
    }
    if (perms.includes('issuanceApplicationDelete')) {
        options.push({
            label: '删除',
            key: 'delete'
        });
    }
    return options;
};

const handleTodoMenu = (key: string, row: any) => {
    switch (key) {
        case 'edit':
            onAddEdit('edit', row.id || '', row);
            break;
        case 'detail':
            onDistributeDetail(row);
            break;
        case 'dispose':
            onPaperDisposal(row);
            break;
        case 'paperDetail':
            onPaperDetail(row);
            break;
        case 'delete':
            onDelete(row);
            break;
    }
};

const onCategoryChange = (value: any, pathInfo: any) => {
    console.log('value', value);
    console.log('pathInfo', pathInfo);
};

const init = () => {
    nextTick(() => {
        searchTablePageRef.value?.initData();
    });
};
const onReset = () => {
    params.value = {
        fileType: '',
        applicant: '',
        status: null,
        issuanceType: null,
        category: null,
        department: null
    };
    init();
};

const handleCheck = (checked: any) => {
    checkedRowKeys.value = checked as string[];
};

const onAddEdit = (type: 'add' | 'edit', oId?: string, row?: IssuanceApplicationRow) => {
    $alert.dialog({
        title: `${type === 'add' ? '新增' : '编辑'}发放申请`,
        width: '60%',
        content: import('./models/issuance-form.vue'),
        props: {
            oId: oId || '',
            row: row || false,
            onClose: () => init()
        }
    });
};
const exportFile = () => {
    window.$dialog.info({
        title: '提示',
        content: '确认后将导出该筛选条件下的数据，是否确认？',
        positiveText: '确认',
        negativeText: '取消',
        onPositiveClick: async () => {
            await $apis.nebula.api.v1.internal.export({
                moduleType: 1,
                params: params.value
            });
            window.$message.info('数据导出成功，请在右上角下拉菜单【数据导出】中查看');
        }
    });
};

const onDistributeDetail = (_row: any) => {
    $alert.dialog({
        title: h('div', { class: 'flex items-center' }, [
            h('span', { class: 'mr-10px' }, '发放回收详情'),
            h(NTag, { type: 'error', size: 'small' }, () => '未回收')
        ]),
        width: '60%',
        content: import('./models/issuance-detail.vue'),
        props: {
            row: _row
        }
    });
};
//
const onPaperDetail = (_row: any) => {
    $alert.dialog({
        title: h('div', { class: 'flex items-center' }, [
            h('span', { class: 'mr-10px' }, '纸质文件处置详情'),
            h(NTag, { type: 'error', size: 'small' }, () => '未回收')
        ]),
        width: '60%',
        content: import('./models/disposal-detail.vue'),
        props: {
            row: _row
        }
    });
};

const onPaperDisposal = (_row: any) => {
    $alert.dialog({
        title: '纸质文件处置',
        width: '60%',
        content: import('./models/paper-disposal-form.vue'),
        props: {
            row: _row
        }
    });
};

const onCancel = (_row: any) => {
    console.log(_row);
    window.$dialog.info({
        title: '撤销文件',
        content: `确认撤销文件，是否确认？`,
        positiveText: '确认',
        negativeText: '取消',
        onPositiveClick: async () => {
            window.$message.success(`文件撤销成功`);
            $alert.dialog.close();
        }
    });
};

const onRecycle = (_row: any) => {
    $alert.dialog({
        title: '回收文件',
        width: '60%',
        content: import('./models/recycle-form.vue'),
        props: {
            row: _row
        }
    });
};

const onDelete = (_row: any) => {
    console.log(_row);
    window.$dialog.error({
        title: '确认提示',
        content: '确认后将删除文件发放申请，是否确认？',
        positiveText: '确认',
        negativeText: '取消',
        onPositiveClick: async () => {
            window.$message.success(`文件发放申请删除成功`);
            $alert.dialog.close();
        }
    });
};
const onDepartmentChange = (value: any, pathInfo: any) => {
    console.log('编制部门变化:', value);
    console.log('完整路径信息:', pathInfo);
};

const statusTagType = (status: string) => {
    switch (status) {
        case '待提交':
            return 'default';
        case '待审批':
            return 'warning';
        case '已驳回':
            return 'error';
        case '已审批':
            return 'success';
        default:
            return 'default';
    }
};
const statusLabel = (status: string) => {
    return status;
};

onMounted(() => {
    // 组件内部会自动调用接口获取数据
});
</script>

<style scoped lang="less"></style>
