import { VxeUI } from 'vxe-table';
import { VxeTooltip, VxeSelect } from 'vxe-pc-ui';
import VxeUIPluginRenderNaive from '@vxe-ui/plugin-render-naive';
// import '@vxe-ui/plugin-render-naive/dist/style.css';
const setupVxeTableConfig = () => {
    VxeUI.use(VxeUIPluginRenderNaive);
    VxeUI.component(VxeTooltip);
    VxeUI.component(VxeSelect);
    VxeUI.setConfig({
        zIndex: 9999,
        table: {
            align: 'center',
            headerAlign: 'center'
        }
    });
}

const tableValid = async (tableRef: globalThis.Ref<any, any>) => {
    const $table = tableRef.value;
    if ($table) {
        const errMap = await $table.validate(true);
        if (errMap) {
            // 获取第一个错误信息
            const firstKey = Object.keys(errMap)[0];
            if (firstKey && errMap[firstKey] && errMap[firstKey].length > 0) {
                const firstError = errMap[firstKey][0];
                throw [
                    [
                        {
                            message: firstError.rule?.$options?.message || firstError.rule?.$options?.content
                        }
                    ]
                ];
            }
        }
        return null;
    }
    return null;
};

export default {
    setupVxeTableConfig,
    tableValid
}